import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/transaction.dart';

class DatabaseService {
  static Database? _database;
  static const String _databaseName = 'expense_tracker.db';
  static const int _databaseVersion = 1;

  // 表名
  static const String _transactionsTable = 'transactions';
  static const String _categoriesTable = 'categories';

  static Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  static Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), _databaseName);
    return await openDatabase(
      path,
      version: _databaseVersion,
      onCreate: _onCreate,
    );
  }

  static Future<void> _onCreate(Database db, int version) async {
    // 创建交易表
    await db.execute('''
      CREATE TABLE $_transactionsTable (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        amount REAL NOT NULL,
        category TEXT NOT NULL,
        type TEXT NOT NULL,
        date INTEGER NOT NULL,
        note TEXT
      )
    ''');

    // 创建分类表
    await db.execute('''
      CREATE TABLE $_categoriesTable (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        icon TEXT NOT NULL,
        type TEXT NOT NULL
      )
    ''');

    // 插入默认分类
    await _insertDefaultCategories(db);
  }

  static Future<void> _insertDefaultCategories(Database db) async {
    // 收入分类
    final incomeCategories = [
      {'name': '工资', 'icon': '💰', 'type': 'income'},
      {'name': '奖金', 'icon': '🎁', 'type': 'income'},
      {'name': '投资', 'icon': '📈', 'type': 'income'},
      {'name': '其他收入', 'icon': '💵', 'type': 'income'},
    ];

    // 支出分类
    final expenseCategories = [
      {'name': '餐饮', 'icon': '🍽️', 'type': 'expense'},
      {'name': '交通', 'icon': '🚗', 'type': 'expense'},
      {'name': '购物', 'icon': '🛒', 'type': 'expense'},
      {'name': '娱乐', 'icon': '🎮', 'type': 'expense'},
      {'name': '医疗', 'icon': '🏥', 'type': 'expense'},
      {'name': '教育', 'icon': '📚', 'type': 'expense'},
      {'name': '住房', 'icon': '🏠', 'type': 'expense'},
      {'name': '其他支出', 'icon': '💸', 'type': 'expense'},
    ];

    for (var category in [...incomeCategories, ...expenseCategories]) {
      await db.insert(_categoriesTable, category);
    }
  }

  // 交易相关操作
  static Future<int> insertTransaction(Transaction transaction) async {
    final db = await database;
    return await db.insert(_transactionsTable, transaction.toMap());
  }

  static Future<List<Transaction>> getTransactions() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _transactionsTable,
      orderBy: 'date DESC',
    );
    return List.generate(maps.length, (i) => Transaction.fromMap(maps[i]));
  }

  static Future<List<Transaction>> getTransactionsByDateRange(
      DateTime start, DateTime end) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _transactionsTable,
      where: 'date >= ? AND date <= ?',
      whereArgs: [start.millisecondsSinceEpoch, end.millisecondsSinceEpoch],
      orderBy: 'date DESC',
    );
    return List.generate(maps.length, (i) => Transaction.fromMap(maps[i]));
  }

  static Future<int> updateTransaction(Transaction transaction) async {
    final db = await database;
    return await db.update(
      _transactionsTable,
      transaction.toMap(),
      where: 'id = ?',
      whereArgs: [transaction.id],
    );
  }

  static Future<int> deleteTransaction(int id) async {
    final db = await database;
    return await db.delete(
      _transactionsTable,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // 分类相关操作
  static Future<List<Category>> getCategories() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(_categoriesTable);
    return List.generate(maps.length, (i) => Category.fromMap(maps[i]));
  }

  static Future<List<Category>> getCategoriesByType(TransactionType type) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _categoriesTable,
      where: 'type = ?',
      whereArgs: [type.toString().split('.').last],
    );
    return List.generate(maps.length, (i) => Category.fromMap(maps[i]));
  }

  static Future<int> insertCategory(Category category) async {
    final db = await database;
    return await db.insert(_categoriesTable, category.toMap());
  }

  static Future<int> deleteCategory(int id) async {
    final db = await database;
    return await db.delete(
      _categoriesTable,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // 统计相关
  static Future<double> getTotalIncome() async {
    final db = await database;
    final result = await db.rawQuery(
      'SELECT SUM(amount) as total FROM $_transactionsTable WHERE type = ?',
      ['income'],
    );
    return result.first['total'] as double? ?? 0.0;
  }

  static Future<double> getTotalExpense() async {
    final db = await database;
    final result = await db.rawQuery(
      'SELECT SUM(amount) as total FROM $_transactionsTable WHERE type = ?',
      ['expense'],
    );
    return result.first['total'] as double? ?? 0.0;
  }

  static Future<double> getBalance() async {
    final income = await getTotalIncome();
    final expense = await getTotalExpense();
    return income - expense;
  }
}
