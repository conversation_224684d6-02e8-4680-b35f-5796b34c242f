{"sourceFile": "android/build.gradle.kts", "activeCommit": 0, "commits": [{"activePatchIndex": 6, "patches": [{"date": 1753406280802, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1753406324489, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,16 +1,15 @@\n buildscript {\n-    ext {\n-        set(\"agp_version\", \"8.3.0\")\n-        set(\"kotlin_version\", \"1.9.22\")\n-    }\n+    val agp_version by extra(\"8.3.0\")\n+    val kotlin_version by extra(\"1.9.22\")\n+    \n     repositories {\n         google()\n         mavenCentral()\n     }\n     dependencies {\n-        classpath(\"com.android.tools.build:gradle:${ext.get(\"agp_version\")}\")\n-        classpath(\"org.jetbrains.kotlin:kotlin-gradle-plugin:${ext.get(\"kotlin_version\")}\")\n+        classpath(\"com.android.tools.build:gradle:$agp_version\")\n+        classpath(\"org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version\")\n     }\n }\n \n allprojects {\n"}, {"date": 1753407112034, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -2,10 +2,13 @@\n     val agp_version by extra(\"8.3.0\")\n     val kotlin_version by extra(\"1.9.22\")\n     \n     repositories {\n-        google()\n-        mavenCentral()\n+        // google()\n+        // mavenCentral()\n+        maven { url 'https://maven.aliyun.com/repository/google' }\n+        maven { url 'https://maven.aliyun.com/repository/jcenter' }\n+        maven { url 'https://maven.aliyun.com/repository/public' }\n     }\n     dependencies {\n         classpath(\"com.android.tools.build:gradle:$agp_version\")\n         classpath(\"org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version\")\n@@ -32,5 +35,5 @@\n }\n \n tasks.register<Delete>(\"clean\") {\n     delete(rootProject.layout.buildDirectory)\n-}\n\\ No newline at end of file\n+}\n"}, {"date": 1753407511614, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -4,11 +4,12 @@\n     \n     repositories {\n         // google()\n         // mavenCentral()\n-        maven { url 'https://maven.aliyun.com/repository/google' }\n-        maven { url 'https://maven.aliyun.com/repository/jcenter' }\n-        maven { url 'https://maven.aliyun.com/repository/public' }\n+        maven { url = uri(\"https://maven.aliyun.com/repository/google\") }\n+        maven { url = uri(\"https://maven.aliyun.com/repository/jcenter\") }\n+        maven { url = uri(\"https://maven.aliyun.com/repository/public\") }\n+\n     }\n     dependencies {\n         classpath(\"com.android.tools.build:gradle:$agp_version\")\n         classpath(\"org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version\")\n"}, {"date": 1753407579290, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -2,10 +2,10 @@\n     val agp_version by extra(\"8.3.0\")\n     val kotlin_version by extra(\"1.9.22\")\n     \n     repositories {\n-        // google()\n-        // mavenCentral()\n+        google()\n+        mavenCentral()\n         maven { url = uri(\"https://maven.aliyun.com/repository/google\") }\n         maven { url = uri(\"https://maven.aliyun.com/repository/jcenter\") }\n         maven { url = uri(\"https://maven.aliyun.com/repository/public\") }\n \n"}, {"date": 1753407955913, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -21,9 +21,28 @@\n         google()\n         mavenCentral()\n     }\n }\n+android {\n+    compileSdk = 34\n \n+    defaultConfig {\n+        applicationId = \"com.example.flutter_application_1\"\n+        minSdk = 21\n+        targetSdk = 34\n+        versionCode = 1\n+        versionName = \"1.0\"\n+    }\n+\n+    ndkVersion = \"27.0.12077973\"\n+\n+    compileOptions {\n+        sourceCompatibility = JavaVersion.VERSION_11\n+        targetCompatibility = JavaVersion.VERSION_11\n+    }\n+}\n+\n+\n val newBuildDir: Directory = rootProject.layout.buildDirectory.dir(\"../../build\").get()\n rootProject.layout.buildDirectory.value(newBuildDir)\n \n subprojects {\n"}, {"date": 1753408030424, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -21,28 +21,9 @@\n         google()\n         mavenCentral()\n     }\n }\n-android {\n-    compileSdk = 34\n \n-    defaultConfig {\n-        applicationId = \"com.example.flutter_application_1\"\n-        minSdk = 21\n-        targetSdk = 34\n-        versionCode = 1\n-        versionName = \"1.0\"\n-    }\n-\n-    ndkVersion = \"27.0.12077973\"\n-\n-    compileOptions {\n-        sourceCompatibility = JavaVersion.VERSION_11\n-        targetCompatibility = JavaVersion.VERSION_11\n-    }\n-}\n-\n-\n val newBuildDir: Directory = rootProject.layout.buildDirectory.dir(\"../../build\").get()\n rootProject.layout.buildDirectory.value(newBuildDir)\n \n subprojects {\n"}], "date": 1753406280802, "name": "Commit-0", "content": "buildscript {\n    ext {\n        set(\"agp_version\", \"8.3.0\")\n        set(\"kotlin_version\", \"1.9.22\")\n    }\n    repositories {\n        google()\n        mavenCentral()\n    }\n    dependencies {\n        classpath(\"com.android.tools.build:gradle:${ext.get(\"agp_version\")}\")\n        classpath(\"org.jetbrains.kotlin:kotlin-gradle-plugin:${ext.get(\"kotlin_version\")}\")\n    }\n}\n\nallprojects {\n    repositories {\n        google()\n        mavenCentral()\n    }\n}\n\nval newBuildDir: Directory = rootProject.layout.buildDirectory.dir(\"../../build\").get()\nrootProject.layout.buildDirectory.value(newBuildDir)\n\nsubprojects {\n    val newSubprojectBuildDir: Directory = newBuildDir.dir(project.name)\n    project.layout.buildDirectory.value(newSubprojectBuildDir)\n}\n\nsubprojects {\n    project.evaluationDependsOn(\":app\")\n}\n\ntasks.register<Delete>(\"clean\") {\n    delete(rootProject.layout.buildDirectory)\n}"}]}