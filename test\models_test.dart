import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_application_1/models/transaction.dart';

void main() {
  group('Transaction Model Tests', () {
    test('Transaction creation and properties', () {
      final transaction = Transaction(
        title: '测试交易',
        amount: 100.0,
        category: '餐饮',
        type: TransactionType.expense,
        date: DateTime.now(),
        note: '测试备注',
      );

      expect(transaction.title, '测试交易');
      expect(transaction.amount, 100.0);
      expect(transaction.category, '餐饮');
      expect(transaction.type, TransactionType.expense);
      expect(transaction.note, '测试备注');
    });

    test('Transaction toMap serialization', () {
      final date = DateTime(2024, 1, 1, 12, 0);
      final transaction = Transaction(
        id: 1,
        title: '测试交易',
        amount: 100.0,
        category: '餐饮',
        type: TransactionType.expense,
        date: date,
        note: '测试备注',
      );

      final map = transaction.toMap();

      expect(map['id'], 1);
      expect(map['title'], '测试交易');
      expect(map['amount'], 100.0);
      expect(map['category'], '餐饮');
      expect(map['type'], 'expense');
      expect(map['date'], date.millisecondsSinceEpoch);
      expect(map['note'], '测试备注');
    });

    test('Transaction fromMap deserialization', () {
      final date = DateTime(2024, 1, 1, 12, 0);
      final map = {
        'id': 1,
        'title': '测试交易',
        'amount': 100.0,
        'category': '餐饮',
        'type': 'expense',
        'date': date.millisecondsSinceEpoch,
        'note': '测试备注',
      };

      final transaction = Transaction.fromMap(map);

      expect(transaction.id, 1);
      expect(transaction.title, '测试交易');
      expect(transaction.amount, 100.0);
      expect(transaction.category, '餐饮');
      expect(transaction.type, TransactionType.expense);
      expect(transaction.date, date);
      expect(transaction.note, '测试备注');
    });

    test('Transaction copyWith method', () {
      final originalTransaction = Transaction(
        id: 1,
        title: '原始交易',
        amount: 100.0,
        category: '餐饮',
        type: TransactionType.expense,
        date: DateTime.now(),
        note: '原始备注',
      );

      final updatedTransaction = originalTransaction.copyWith(
        title: '更新交易',
        amount: 200.0,
      );

      expect(updatedTransaction.id, originalTransaction.id);
      expect(updatedTransaction.title, '更新交易');
      expect(updatedTransaction.amount, 200.0);
      expect(updatedTransaction.category, originalTransaction.category);
      expect(updatedTransaction.type, originalTransaction.type);
      expect(updatedTransaction.date, originalTransaction.date);
      expect(updatedTransaction.note, originalTransaction.note);
    });
  });

  group('Category Model Tests', () {
    test('Category creation and properties', () {
      final category = Category(
        name: '餐饮',
        icon: '🍽️',
        type: TransactionType.expense,
      );

      expect(category.name, '餐饮');
      expect(category.icon, '🍽️');
      expect(category.type, TransactionType.expense);
    });

    test('Category toMap serialization', () {
      final category = Category(
        id: 1,
        name: '餐饮',
        icon: '🍽️',
        type: TransactionType.expense,
      );

      final map = category.toMap();

      expect(map['id'], 1);
      expect(map['name'], '餐饮');
      expect(map['icon'], '🍽️');
      expect(map['type'], 'expense');
    });

    test('Category fromMap deserialization', () {
      final map = {
        'id': 1,
        'name': '餐饮',
        'icon': '🍽️',
        'type': 'expense',
      };

      final category = Category.fromMap(map);

      expect(category.id, 1);
      expect(category.name, '餐饮');
      expect(category.icon, '🍽️');
      expect(category.type, TransactionType.expense);
    });
  });

  group('TransactionType Enum Tests', () {
    test('TransactionType values', () {
      expect(TransactionType.values.length, 2);
      expect(TransactionType.values.contains(TransactionType.income), true);
      expect(TransactionType.values.contains(TransactionType.expense), true);
    });

    test('TransactionType string conversion', () {
      expect(TransactionType.income.toString().split('.').last, 'income');
      expect(TransactionType.expense.toString().split('.').last, 'expense');
    });
  });
}
