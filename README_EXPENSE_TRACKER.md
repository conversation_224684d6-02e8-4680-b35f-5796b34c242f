# Flutter 记账应用

一个使用 Flutter 开发的简洁易用的个人记账应用，支持本地数据存储，无需登录即可使用。

## 功能特点

### 🏠 主页面
- **余额概览**: 显示当前总余额、总收入、总支出
- **交易记录**: 按时间倒序显示所有收支记录
- **快速操作**: 长按记录可删除
- **下拉刷新**: 支持下拉刷新数据

### ➕ 添加记录
- **收入/支出**: 支持两种交易类型
- **分类管理**: 预设多种分类，支持自定义
- **详细信息**: 标题、金额、分类、日期时间、备注
- **表单验证**: 确保数据完整性和有效性

### 📊 统计分析
- **月度统计**: 按月查看收支情况
- **分类统计**: 各分类支出/收入占比分析
- **可视化图表**: 直观的进度条显示
- **历史数据**: 支持查看不同月份的统计

### ⚙️ 设置管理
- **分类管理**: 添加/删除自定义分类
- **数据管理**: 备份、恢复、清空功能（开发中）
- **应用信息**: 关于页面和版本信息

## 技术架构

### 数据存储
- **SQLite**: 本地数据库存储
- **数据模型**: Transaction（交易）和 Category（分类）
- **数据服务**: DatabaseService 统一管理数据操作

### 界面设计
- **Material Design 3**: 现代化的 UI 设计
- **响应式布局**: 适配不同屏幕尺寸
- **流畅动画**: 页面切换和交互动画

### 项目结构
```
lib/
├── main.dart                 # 应用入口和主导航
├── models/
│   └── transaction.dart      # 数据模型
├── services/
│   └── database_service.dart # 数据库服务
└── screens/
    ├── home_screen.dart      # 主页面
    ├── add_transaction_screen.dart  # 添加记录页面
    ├── statistics_screen.dart       # 统计页面
    └── settings_screen.dart         # 设置页面
```

## 依赖包

- `sqflite`: SQLite 数据库支持
- `intl`: 国际化和日期格式化
- `flutter/material`: Material Design 组件

## 安装运行

1. 确保已安装 Flutter SDK
2. 克隆项目到本地
3. 运行以下命令：

```bash
# 获取依赖
flutter pub get

# 运行应用
flutter run
```

## 使用说明

### 首次使用
1. 应用启动后会自动创建本地数据库
2. 预设了常用的收入和支出分类
3. 点击右下角的 "+" 按钮添加第一笔记录

### 添加记录
1. 选择收入或支出类型
2. 填写标题和金额（必填）
3. 选择分类
4. 设置日期时间（默认当前时间）
5. 添加备注（可选）
6. 点击保存

### 查看统计
1. 切换到统计页面
2. 点击月份选择器查看不同月份
3. 查看收支概览和分类统计

### 管理分类
1. 进入设置页面
2. 在分类管理中添加自定义分类
3. 设置分类名称、图标和类型

## 数据安全

- **本地存储**: 所有数据存储在设备本地，保护隐私
- **无网络依赖**: 完全离线使用，无需担心数据泄露
- **数据持久化**: 应用卸载前数据永久保存

## 未来计划

- [ ] 数据备份和恢复功能
- [ ] 更多图表类型（饼图、折线图）
- [ ] 预算管理功能
- [ ] 多账户支持
- [ ] 数据导出（CSV、Excel）
- [ ] 深色主题支持

## 开发者信息

这是一个开源的 Flutter 记账应用，欢迎贡献代码和提出建议。

### 版本信息
- 当前版本: 1.0.0
- Flutter 版本: >=2.19.4
- 支持平台: Android, iOS, Windows, macOS, Linux

---

**注意**: 这是一个本地应用，请定期备份重要数据。
