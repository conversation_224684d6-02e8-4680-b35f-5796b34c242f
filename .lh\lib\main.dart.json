{"sourceFile": "lib/main.dart", "activeCommit": 0, "commits": [{"activePatchIndex": 64, "patches": [{"date": 1751811960693, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1751814026257, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -36,9 +36,9 @@\n \r\n     return Scaffold(\r\n       body: Column(\r\n         children: [\r\n-          Text('A random idea:'),\r\n+          Text('A random AWESOME idea:'),\r\n           Text(appState.current.asLowerCase),\r\n         ],\r\n       ),\r\n     );\r\n"}, {"date": 1751814152627, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -38,8 +38,13 @@\n       body: Column(\r\n         children: [\r\n           Text('A random AWESOME idea:'),\r\n           Text(appState.current.asLowerCase),\r\n+          ElevatedButton(\r\n+            onPressed: () {\r\n+            \r\n+          }\r\n+          , child: Text(\"Next\"))\r\n         ],\r\n       ),\r\n     );\r\n   }\r\n"}, {"date": 1751814199328, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -38,14 +38,13 @@\n       body: Column(\r\n         children: [\r\n           Text('A random AWESOME idea:'),\r\n           Text(appState.current.asLowerCase),\r\n-          ElevatedButton(\r\n-            onPressed: () {\r\n-            \r\n-          }\r\n-          , child: Text(\"Next\"))\r\n+          ElevatedButton(onPressed: () {\r\n+            print('button pressed!');\r\n+          }, \r\n+          child: Text(\"Next\"))\r\n         ],\r\n\\ No newline at end of file\n       ),\r\n     );\r\n   }\r\n-}\n+}\r\n"}, {"date": 1751814851457, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -26,8 +26,12 @@\n }\r\n \r\n class MyAppState extends ChangeNotifier {\r\n   var current = WordPair.random();\r\n+  void getNext() {\r\n+    current = WordPair.random();\r\n+    notifyListeners();\r\n+  }\r\n }\r\n \r\n class MyHomePage extends StatelessWidget {\r\n   @override\r\n@@ -46,5 +50,5 @@\n         ],\r\n       ),\r\n     );\r\n   }\r\n-}\n\\ No newline at end of file\n+}\r\n"}, {"date": 1751814949496, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -43,9 +43,9 @@\n         children: [\r\n           Text('A random AWESOME idea:'),\r\n           Text(appState.current.asLowerCase),\r\n           ElevatedButton(onPressed: () {\r\n-            print('button pressed!');\r\n+            appState.getNext();\r\n           }, \r\n           child: Text(\"Next\"))\r\n         ],\r\n       ),\r\n"}, {"date": 1751815146925, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -36,14 +36,15 @@\n class MyHomePage extends StatelessWidget {\r\n   @override\r\n   Widget build(BuildContext context) {\r\n     var appState = context.watch<MyAppState>();\r\n+    var pair = appState.current;\r\n \r\n     return Scaffold(\r\n       body: Column(\r\n         children: [\r\n           Text('A random AWESOME idea:'),\r\n-          Text(appState.current.asLowerCase),\r\n+          Text(pair.asLowerCase),\r\n           ElevatedButton(onPressed: () {\r\n             appState.getNext();\r\n           }, \r\n           child: Text(\"Next\"))\r\n"}, {"date": 1751815271963, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -43,12 +43,14 @@\n       body: Column(\r\n         children: [\r\n           Text('A random AWESOME idea:'),\r\n           Text(pair.asLowerCase),\r\n-          ElevatedButton(onPressed: () {\r\n-            appState.getNext();\r\n-          }, \r\n-          child: Text(\"Next\"))\r\n+          ElevatedButton(\r\n+            onPressed: () {\r\n+              appState.getNext();\r\n+            }, \r\n+            child: Text(\"Next\"),\r\n+          )\r\n         ],\r\n       ),\r\n     );\r\n   }\r\n"}, {"date": 1751815698034, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -42,9 +42,9 @@\n     return Scaffold(\r\n       body: Column(\r\n         children: [\r\n           Text('A random AWESOME idea:'),\r\n-          Text(pair.asLowerCase),\r\n+          Big<PERSON>ard(pair: pair),\r\n           ElevatedButton(\r\n             onPressed: () {\r\n               appState.getNext();\r\n             }, \r\n@@ -54,4 +54,18 @@\n       ),\r\n     );\r\n   }\r\n }\r\n+\r\n+class BigCard extends StatelessWidget {\r\n+  const BigCard({\r\n+    super.key,\r\n+    required this.pair,\r\n+  });\r\n+\r\n+  final WordPair pair;\r\n+\r\n+  @override\r\n+  Widget build(BuildContext context) {\r\n+    return Text(pair.asLowerCase);\r\n+  }\r\n+}\r\n"}, {"date": 1751815737005, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -65,7 +65,10 @@\n   final WordPair pair;\r\n \r\n   @override\r\n   Widget build(BuildContext context) {\r\n-    return Text(pair.asLowerCase);\r\n+    return Padding(\r\n+      padding: const EdgeInsets.all(20.0),\r\n+      child: Text(pair.asLowerCase),\r\n+    );\r\n   }\r\n }\r\n"}, {"date": 1751815950824, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -65,10 +65,15 @@\n   final WordPair pair;\r\n \r\n   @override\r\n   Widget build(BuildContext context) {\r\n-    return Padding(\r\n-      padding: const EdgeInsets.all(20.0),\r\n-      child: Text(pair.asLowerCase),\r\n+    final theme = Theme.of(context);\r\n+\r\n+    return Card(\r\n+      color: theme.colorScheme.primary,\r\n+      child: Padding(\r\n+        padding: const EdgeInsets.all(20.0),\r\n+        child: Text(pair.asLowerCase),\r\n+      ),\r\n     );\r\n   }\r\n }\r\n"}, {"date": 1751815966880, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -16,9 +16,9 @@\n       child: MaterialApp(\r\n         title: 'Namer App',\r\n         theme: ThemeData(\r\n           useMaterial3: true,\r\n-          colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepOrange),\r\n+          colorScheme: ColorScheme.fromSeed(seedColor: const Color.fromARGB(255, 1, 26, 248)),\r\n         ),\r\n         home: MyHomePage(),\r\n       ),\r\n     );\r\n"}, {"date": 1751816023388, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -16,9 +16,9 @@\n       child: MaterialApp(\r\n         title: 'Namer App',\r\n         theme: ThemeData(\r\n           useMaterial3: true,\r\n-          colorScheme: ColorScheme.fromSeed(seedColor: const Color.fromARGB(255, 1, 26, 248)),\r\n+          colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),\r\n         ),\r\n         home: MyHomePage(),\r\n       ),\r\n     );\r\n"}, {"date": 1751816112318, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -66,9 +66,11 @@\n \r\n   @override\r\n   Widget build(BuildContext context) {\r\n     final theme = Theme.of(context);\r\n-\r\n+    final style = theme.textTheme.displayMedium!.copyWith(\r\n+      color: theme.colorScheme.onPrimary,\r\n+    );\r\n     return Card(\r\n       color: theme.colorScheme.primary,\r\n       child: Padding(\r\n         padding: const EdgeInsets.all(20.0),\r\n"}, {"date": 1751816129010, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -73,9 +73,9 @@\n     return Card(\r\n       color: theme.colorScheme.primary,\r\n       child: Padding(\r\n         padding: const EdgeInsets.all(20.0),\r\n-        child: Text(pair.asLowerCase),\r\n+        child: Text(pair.asLowerCase,style: style),\r\n       ),\r\n     );\r\n   }\r\n }\r\n"}, {"date": 1751816287161, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -73,9 +73,13 @@\n     return Card(\r\n       color: theme.colorScheme.primary,\r\n       child: Padding(\r\n         padding: const EdgeInsets.all(20.0),\r\n-        child: Text(pair.asLowerCase,style: style),\r\n+        child: Text(\r\n+          pair.asLowerCase,\r\n+          style: style,\r\n+          semanticsLabel: \"${pair.first} ${pair.second}}\"\r\n+        ),\r\n       ),\r\n     );\r\n   }\r\n }\r\n"}, {"date": 1751816585210, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -40,8 +40,9 @@\n     var pair = appState.current;\r\n \r\n     return Scaffold(\r\n       body: Column(\r\n+        mainAxisAlignment: MainAxisAlignment.center,\r\n         children: [\r\n           Text('A random AWESOME idea:'),\r\n           BigCard(pair: pair),\r\n           ElevatedButton(\r\n"}, {"date": 1751816593042, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -39,20 +39,22 @@\n     var appState = context.watch<MyAppState>();\r\n     var pair = appState.current;\r\n \r\n     return Scaffold(\r\n-      body: Column(\r\n-        mainAxisAlignment: MainAxisAlignment.center,\r\n-        children: [\r\n-          Text('A random AWESOME idea:'),\r\n-          BigCard(pair: pair),\r\n-          ElevatedButton(\r\n-            onPressed: () {\r\n-              appState.getNext();\r\n-            }, \r\n-            child: Text(\"Next\"),\r\n-          )\r\n-        ],\r\n+      body: Center(\r\n+        child: Column(\r\n+          mainAxisAlignment: MainAxisAlignment.center,\r\n+          children: [\r\n+            Text('A random AWESOME idea:'),\r\n+            BigCard(pair: pair),\r\n+            ElevatedButton(\r\n+              onPressed: () {\r\n+                appState.getNext();\r\n+              }, \r\n+              child: Text(\"Next\"),\r\n+            )\r\n+          ],\r\n+        ),\r\n       ),\r\n     );\r\n   }\r\n }\r\n"}, {"date": 1751816647148, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -43,9 +43,8 @@\n       body: Center(\r\n         child: Column(\r\n           mainAxisAlignment: MainAxisAlignment.center,\r\n           children: [\r\n-            Text('A random AWESOME idea:'),\r\n             BigCard(pair: pair),\r\n             ElevatedButton(\r\n               onPressed: () {\r\n                 appState.getNext();\r\n"}, {"date": 1751816677705, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -44,8 +44,9 @@\n         child: Column(\r\n           mainAxisAlignment: MainAxisAlignment.center,\r\n           children: [\r\n             Big<PERSON><PERSON>(pair: pair),\r\n+            <PERSON><PERSON><PERSON><PERSON>(height: 10,),\r\n             ElevatedButton(\r\n               onPressed: () {\r\n                 appState.getNext();\r\n               }, \r\n"}, {"date": 1751900494126, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -30,8 +30,17 @@\n   void getNext() {\r\n     current = WordPair.random();\r\n     notifyListeners();\r\n   }\r\n+  var favorites = <WordPair>[];\r\n+  void toggleFavorite() {\r\n+    if (favorites.contains(current)) {\r\n+      favorites.remove(current);\r\n+    } else {\r\n+      favorites.add(current);\r\n+    }\r\n+    notifyListeners();\r\n+  }\r\n }\r\n \r\n class MyHomePage extends StatelessWidget {\r\n   @override\r\n"}, {"date": 1751900598382, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -54,13 +54,18 @@\n           mainAxisAlignment: MainAxisAlignment.center,\r\n           children: [\r\n             <PERSON><PERSON><PERSON>(pair: pair),\r\n             <PERSON><PERSON><PERSON><PERSON>(height: 10,),\r\n-            ElevatedButton(\r\n-              onPressed: () {\r\n-                appState.getNext();\r\n-              }, \r\n-              child: Text(\"Next\"),\r\n+            Row(\r\n+              mainAxisSize: MainAxisSize.min,\r\n+              children: [\r\n+                ElevatedButton(\r\n+                  onPressed: () {\r\n+                    appState.getNext();\r\n+                  }, \r\n+                  child: Text(\"Next\"),\r\n+                ),\r\n+              ],\r\n             )\r\n           ],\r\n         ),\r\n       ),\r\n"}, {"date": 1751900710415, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -59,8 +59,14 @@\n               mainAxisSize: MainAxisSize.min,\r\n               children: [\r\n                 ElevatedButton(\r\n                   onPressed: () {\r\n+                    appState.toggleFavorite();\r\n+                  }, \r\n+                  child: Text(\"Like\"),\r\n+                ),\r\n+                ElevatedButton(\r\n+                  onPressed: () {\r\n                     appState.getNext();\r\n                   }, \r\n                   child: Text(\"Next\"),\r\n                 ),\r\n"}, {"date": 1751900772990, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -37,8 +37,9 @@\n       favorites.remove(current);\r\n     } else {\r\n       favorites.add(current);\r\n     }\r\n+    print(favorites.length);\r\n     notifyListeners();\r\n   }\r\n }\r\n \r\n"}, {"date": 1751900928303, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -48,8 +48,14 @@\n   Widget build(BuildContext context) {\r\n     var appState = context.watch<MyAppState>();\r\n     var pair = appState.current;\r\n \r\n+    IconData icon;\r\n+    if (appState.favorites.contains(pair)) {\r\n+      icon = Icons.favorite;\r\n+    } else {\r\n+      icon = Icons.favorite_border;\r\n+    }\r\n     return Scaffold(\r\n       body: Center(\r\n         child: Column(\r\n           mainAxisAlignment: MainAxisAlignment.center,\r\n"}, {"date": 1751901008550, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -30,8 +30,9 @@\n   void getNext() {\r\n     current = WordPair.random();\r\n     notifyListeners();\r\n   }\r\n+\r\n   var favorites = <WordPair>[];\r\n   void toggleFavorite() {\r\n     if (favorites.contains(current)) {\r\n       favorites.remove(current);\r\n@@ -60,22 +61,26 @@\n         child: Column(\r\n           mainAxisAlignment: MainAxisAlignment.center,\r\n           children: [\r\n             BigCard(pair: pair),\r\n-            SizedBox(height: 10,),\r\n+            SizedBox(\r\n+              height: 10,\r\n+            ),\r\n             Row(\r\n               mainAxisSize: MainAxisSize.min,\r\n               children: [\r\n-                ElevatedButton(\r\n+                ElevatedButton.icon(\r\n                   onPressed: () {\r\n                     appState.toggleFavorite();\r\n-                  }, \r\n-                  child: Text(\"Like\"),\r\n+                  },\r\n+                  icon: Icon(icon),\r\n+                  label: Text(\"Like\"),\r\n                 ),\r\n+                <PERSON>zedBox(width: 10),\r\n                 ElevatedButton(\r\n                   onPressed: () {\r\n                     appState.getNext();\r\n-                  }, \r\n+                  },\r\n                   child: Text(\"Next\"),\r\n                 ),\r\n               ],\r\n             )\r\n@@ -103,13 +108,10 @@\n     return Card(\r\n       color: theme.colorScheme.primary,\r\n       child: Padding(\r\n         padding: const EdgeInsets.all(20.0),\r\n-        child: Text(\r\n-          pair.asLowerCase,\r\n-          style: style,\r\n-          semanticsLabel: \"${pair.first} ${pair.second}}\"\r\n-        ),\r\n+        child: Text(pair.asLowerCase,\r\n+            style: style, semanticsLabel: \"${pair.first} ${pair.second}}\"),\r\n       ),\r\n     );\r\n   }\r\n }\r\n"}, {"date": 1751901052185, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -16,9 +16,9 @@\n       child: MaterialApp(\r\n         title: 'Namer App',\r\n         theme: ThemeData(\r\n           useMaterial3: true,\r\n-          colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),\r\n+          colorScheme: ColorScheme.fromSeed(seedColor: Colors.red),\r\n         ),\r\n         home: MyHomePage(),\r\n       ),\r\n     );\r\n"}, {"date": 1751901173343, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -43,11 +43,50 @@\n     notifyListeners();\r\n   }\r\n }\r\n \r\n+// ...\r\n+\r\n class MyHomePage extends StatelessWidget {\r\n   @override\r\n   Widget build(BuildContext context) {\r\n+    return Scaffold(\r\n+      body: Row(\r\n+        children: [\r\n+          SafeArea(\r\n+            child: NavigationRail(\r\n+              extended: false,\r\n+              destinations: [\r\n+                NavigationRailDestination(\r\n+                  icon: Icon(Icons.home),\r\n+                  label: Text('Home'),\r\n+                ),\r\n+                NavigationRailDestination(\r\n+                  icon: Icon(Icons.favorite),\r\n+                  label: Text('Favorites'),\r\n+                ),\r\n+              ],\r\n+              selectedIndex: 0,\r\n+              onDestinationSelected: (value) {\r\n+                print('selected: $value');\r\n+              },\r\n+            ),\r\n+          ),\r\n+          Expanded(\r\n+            child: Container(\r\n+              color: Theme.of(context).colorScheme.primaryContainer,\r\n+              child: GeneratorPage(),\r\n+            ),\r\n+          ),\r\n+        ],\r\n+      ),\r\n+    );\r\n+  }\r\n+}\r\n+\r\n+class GeneratorPage extends StatelessWidget {\r\n+  @override\r\n+  Widget build(BuildContext context) {\r\n     var appState = context.watch<MyAppState>();\r\n     var pair = appState.current;\r\n \r\n     IconData icon;\r\n@@ -55,38 +94,35 @@\n       icon = Icons.favorite;\r\n     } else {\r\n       icon = Icons.favorite_border;\r\n     }\r\n-    return Scaffold(\r\n-      body: Center(\r\n-        child: Column(\r\n-          mainAxisAlignment: MainAxisAlignment.center,\r\n-          children: [\r\n-            BigCard(pair: pair),\r\n-            SizedBox(\r\n-              height: 10,\r\n-            ),\r\n-            Row(\r\n-              mainAxisSize: MainAxisSize.min,\r\n-              children: [\r\n-                ElevatedButton.icon(\r\n-                  onPressed: () {\r\n-                    appState.toggleFavorite();\r\n-                  },\r\n-                  icon: Icon(icon),\r\n-                  label: Text(\"Like\"),\r\n-                ),\r\n-                SizedBox(width: 10),\r\n-                ElevatedButton(\r\n-                  onPressed: () {\r\n-                    appState.getNext();\r\n-                  },\r\n-                  child: Text(\"Next\"),\r\n-                ),\r\n-              ],\r\n-            )\r\n-          ],\r\n-        ),\r\n+\r\n+    return Center(\r\n+      child: Column(\r\n+        mainAxisAlignment: MainAxisAlignment.center,\r\n+        children: [\r\n+          BigCard(pair: pair),\r\n+          SizedBox(height: 10),\r\n+          Row(\r\n+            mainAxisSize: MainAxisSize.min,\r\n+            children: [\r\n+              ElevatedButton.icon(\r\n+                onPressed: () {\r\n+                  appState.toggleFavorite();\r\n+                },\r\n+                icon: Icon(icon),\r\n+                label: Text('Like'),\r\n+              ),\r\n+              SizedBox(width: 10),\r\n+              ElevatedButton(\r\n+                onPressed: () {\r\n+                  appState.getNext();\r\n+                },\r\n+                child: Text('Next'),\r\n+              ),\r\n+            ],\r\n+          ),\r\n+        ],\r\n       ),\r\n     );\r\n   }\r\n }\r\n"}, {"date": 1751901350307, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -53,9 +53,9 @@\n       body: Row(\r\n         children: [\r\n           SafeArea(\r\n             child: NavigationRail(\r\n-              extended: false,\r\n+              extended: true,\r\n               destinations: [\r\n                 NavigationRailDestination(\r\n                   icon: Icon(Icons.home),\r\n                   label: Text('Home'),\r\n"}, {"date": 1751901371244, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -53,9 +53,9 @@\n       body: Row(\r\n         children: [\r\n           SafeArea(\r\n             child: NavigationRail(\r\n-              extended: true,\r\n+              extended: false,\r\n               destinations: [\r\n                 NavigationRailDestination(\r\n                   icon: Icon(Icons.home),\r\n                   label: Text('Home'),\r\n"}, {"date": 1751901663571, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -74,8 +74,9 @@\n           Expanded(\r\n             child: Container(\r\n               color: Theme.of(context).colorScheme.primaryContainer,\r\n               child: GeneratorPage(),\r\n+              child: Text('Hello World'),\r\n             ),\r\n           ),\r\n         ],\r\n       ),\r\n"}, {"date": 1751901672235, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -74,9 +74,8 @@\n           Expanded(\r\n             child: Container(\r\n               color: Theme.of(context).colorScheme.primaryContainer,\r\n               child: GeneratorPage(),\r\n-              child: Text('Hello World'),\r\n             ),\r\n           ),\r\n         ],\r\n       ),\r\n"}, {"date": 1751901863749, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -45,10 +45,16 @@\n }\r\n \r\n // ...\r\n \r\n-class MyHomePage extends StatelessWidget {\r\n+class MyHomePage extends StatefulWidget {\r\n   @override\r\n+  State<MyHomePage> createState() => _MyHomePageState();\r\n+}\r\n+\r\n+class _MyHomePageState extends State<MyHomePage> {\r\n+  var selectedIndex = 0;\r\n+  @override\r\n   Widget build(BuildContext context) {\r\n     return Scaffold(\r\n       body: Row(\r\n         children: [\r\n@@ -64,11 +70,13 @@\n                   icon: Icon(Icons.favorite),\r\n                   label: Text('Favorites'),\r\n                 ),\r\n               ],\r\n-              selectedIndex: 0,\r\n+              selectedIndex: selectedIndex,\r\n               onDestinationSelected: (value) {\r\n-                print('selected: $value');\r\n+                setState(() {\r\n+                  selectedIndex = value;\r\n+                });\r\n               },\r\n             ),\r\n           ),\r\n           Expanded(\r\n"}, {"date": 1751901913805, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -54,8 +54,9 @@\n class _MyHomePageState extends State<MyHomePage> {\r\n   var selectedIndex = 0;\r\n   @override\r\n   Widget build(BuildContext context) {\r\n+    \r\n     return Scaffold(\r\n       body: Row(\r\n         children: [\r\n           SafeArea(\r\n"}, {"date": 1751901971734, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -54,9 +54,19 @@\n class _MyHomePageState extends State<MyHomePage> {\r\n   var selectedIndex = 0;\r\n   @override\r\n   Widget build(BuildContext context) {\r\n-    \r\n+    Widget page;\r\n+    switch (selectedIndex) {\r\n+      case 0:\r\n+        page = GeneratorPage();\r\n+        break;\r\n+      case 1:\r\n+        page = Placeholder();\r\n+        break;\r\n+      default:\r\n+        throw UnimplementedError('no widget for $selectedIndex');\r\n+    }\r\n     return Scaffold(\r\n       body: Row(\r\n         children: [\r\n           SafeArea(\r\n@@ -82,9 +92,9 @@\n           ),\r\n           Expanded(\r\n             child: Container(\r\n               color: Theme.of(context).colorScheme.primaryContainer,\r\n-              child: GeneratorPage(),\r\n+              child: page,\r\n             ),\r\n           ),\r\n         ],\r\n       ),\r\n"}, {"date": 1751902171933, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,6 +1,7 @@\n import 'package:english_words/english_words.dart';\r\n import 'package:flutter/material.dart';\r\n+import 'package:flutter/rendering.dart';\r\n import 'package:provider/provider.dart';\r\n \r\n void main() {\r\n   runApp(MyApp());\r\n@@ -65,40 +66,44 @@\n         break;\r\n       default:\r\n         throw UnimplementedError('no widget for $selectedIndex');\r\n     }\r\n-    return Scaffold(\r\n-      body: Row(\r\n-        children: [\r\n-          SafeArea(\r\n-            child: NavigationRail(\r\n-              extended: false,\r\n-              destinations: [\r\n-                NavigationRailDestination(\r\n-                  icon: Icon(Icons.home),\r\n-                  label: Text('Home'),\r\n+    return LayoutBuilder(\r\n+      builder: (context, constraints) {\r\n+        return Scaffold(\r\n+          body: Row(\r\n+            children: [\r\n+              SafeArea(\r\n+                child: NavigationRail(\r\n+                  extended: constraints.maxWidth >= 600,\r\n+                  destinations: [\r\n+                    NavigationRailDestination(\r\n+                      icon: Icon(Icons.home),\r\n+                      label: Text('Home'),\r\n+                    ),\r\n+                    NavigationRailDestination(\r\n+                      icon: Icon(Icons.favorite),\r\n+                      label: Text('Favorites'),\r\n+                    ),\r\n+                  ],\r\n+                  selectedIndex: selectedIndex,\r\n+                  onDestinationSelected: (value) {\r\n+                    setState(() {\r\n+                      selectedIndex = value;\r\n+                    });\r\n+                  },\r\n                 ),\r\n-                NavigationRailDestination(\r\n-                  icon: Icon(Icons.favorite),\r\n-                  label: Text('Favorites'),\r\n+              ),\r\n+              Expanded(\r\n+                child: Container(\r\n+                  color: Theme.of(context).colorScheme.primaryContainer,\r\n+                  child: page,\r\n                 ),\r\n-              ],\r\n-              selectedIndex: selectedIndex,\r\n-              onDestinationSelected: (value) {\r\n-                setState(() {\r\n-                  selectedIndex = value;\r\n-                });\r\n-              },\r\n-            ),\r\n+              ),\r\n+            ],\r\n           ),\r\n-          Expanded(\r\n-            child: Container(\r\n-              color: Theme.of(context).colorScheme.primaryContainer,\r\n-              child: page,\r\n-            ),\r\n-          ),\r\n-        ],\r\n-      ),\r\n+        );\r\n+      }\r\n     );\r\n   }\r\n }\r\n \r\n"}, {"date": 1751902218412, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -73,9 +73,9 @@\n           body: Row(\r\n             children: [\r\n               SafeArea(\r\n                 child: NavigationRail(\r\n-                  extended: constraints.maxWidth >= 600,\r\n+                  extended: constraints.maxWidth >= 100,\r\n                   destinations: [\r\n                     NavigationRailDestination(\r\n                       icon: Icon(Icons.home),\r\n                       label: Text('Home'),\r\n"}, {"date": 1751902225969, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -73,9 +73,9 @@\n           body: Row(\r\n             children: [\r\n               SafeArea(\r\n                 child: NavigationRail(\r\n-                  extended: constraints.maxWidth >= 100,\r\n+                  extended: constraints.maxWidth >= 600,\r\n                   destinations: [\r\n                     NavigationRailDestination(\r\n                       icon: Icon(Icons.home),\r\n                       label: Text('Home'),\r\n"}, {"date": 1751902349826, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -174,4 +174,8 @@\n       ),\r\n     );\r\n   }\r\n }\r\n+\r\n+\r\n+class FavoritesPage extends StatelessWidget { \r\n+}\n\\ No newline at end of file\n"}, {"date": 1751902384117, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -176,6 +176,33 @@\n   }\r\n }\r\n \r\n \r\n-class FavoritesPage extends StatelessWidget { \r\n+// ...\r\n+\r\n+class FavoritesPage extends StatelessWidget {\r\n+  @override\r\n+  Widget build(BuildContext context) {\r\n+    var appState = context.watch<MyAppState>();\r\n+\r\n+    if (appState.favorites.isEmpty) {\r\n+      return Center(\r\n+        child: Text('No favorites yet.'),\r\n+      );\r\n+    }\r\n+\r\n+    return ListView(\r\n+      children: [\r\n+        Padding(\r\n+          padding: const EdgeInsets.all(20),\r\n+          child: Text('You have '\r\n+              '${appState.favorites.length} favorites:'),\r\n+        ),\r\n+        for (var pair in appState.favorites)\r\n+          ListTile(\r\n+            leading: Icon(Icons.favorite),\r\n+            title: Text(pair.asLowerCase),\r\n+          ),\r\n+      ],\r\n+    );\r\n+  }\r\n }\n\\ No newline at end of file\n"}, {"date": 1751902441622, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -61,9 +61,9 @@\n       case 0:\r\n         page = GeneratorPage();\r\n         break;\r\n       case 1:\r\n-        page = Placeholder();\r\n+        page = FavoritesPage();\r\n         break;\r\n       default:\r\n         throw UnimplementedError('no widget for $selectedIndex');\r\n     }\r\n"}, {"date": 1753178780655, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,1 @@\n+import 'package:flutter/material.dart';\r\n"}, {"date": 1753178807607, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,209 +1,4 @@\n import 'package:flutter/material.dart';\r\n-import 'package:english_words/english_words.dart';\r\n-import 'package:flutter/material.dart';\r\n-import 'package:flutter/rendering.dart';\r\n-import 'package:provider/provider.dart';\r\n-\r\n void main() {\r\n-  runApp(MyApp());\r\n-}\r\n-\r\n-class MyApp extends StatelessWidget {\r\n-  const MyApp({super.key});\r\n-\r\n-  @override\r\n-  Widget build(BuildContext context) {\r\n-    return ChangeNotifierProvider(\r\n-      create: (context) => MyAppState(),\r\n-      child: MaterialApp(\r\n-        title: 'Namer App',\r\n-        theme: ThemeData(\r\n-          useMaterial3: true,\r\n-          colorScheme: ColorScheme.fromSeed(seedColor: Colors.red),\r\n-        ),\r\n-        home: MyHomePage(),\r\n-      ),\r\n-    );\r\n-  }\r\n-}\r\n-\r\n-class MyAppState extends ChangeNotifier {\r\n-  var current = WordPair.random();\r\n-  void getNext() {\r\n-    current = WordPair.random();\r\n-    notifyListeners();\r\n-  }\r\n-\r\n-  var favorites = <WordPair>[];\r\n-  void toggleFavorite() {\r\n-    if (favorites.contains(current)) {\r\n-      favorites.remove(current);\r\n-    } else {\r\n-      favorites.add(current);\r\n-    }\r\n-    print(favorites.length);\r\n-    notifyListeners();\r\n-  }\r\n-}\r\n-\r\n-// ...\r\n-\r\n-class MyHomePage extends StatefulWidget {\r\n-  @override\r\n-  State<MyHomePage> createState() => _MyHomePageState();\r\n-}\r\n-\r\n-class _MyHomePageState extends State<MyHomePage> {\r\n-  var selectedIndex = 0;\r\n-  @override\r\n-  Widget build(BuildContext context) {\r\n-    Widget page;\r\n-    switch (selectedIndex) {\r\n-      case 0:\r\n-        page = GeneratorPage();\r\n-        break;\r\n-      case 1:\r\n-        page = FavoritesPage();\r\n-        break;\r\n-      default:\r\n-        throw UnimplementedError('no widget for $selectedIndex');\r\n-    }\r\n-    return LayoutBuilder(\r\n-      builder: (context, constraints) {\r\n-        return Scaffold(\r\n-          body: Row(\r\n-            children: [\r\n-              SafeArea(\r\n-                child: NavigationRail(\r\n-                  extended: constraints.maxWidth >= 600,\r\n-                  destinations: [\r\n-                    NavigationRailDestination(\r\n-                      icon: Icon(Icons.home),\r\n-                      label: Text('Home'),\r\n-                    ),\r\n-                    NavigationRailDestination(\r\n-                      icon: Icon(Icons.favorite),\r\n-                      label: Text('Favorites'),\r\n-                    ),\r\n-                  ],\r\n-                  selectedIndex: selectedIndex,\r\n-                  onDestinationSelected: (value) {\r\n-                    setState(() {\r\n-                      selectedIndex = value;\r\n-                    });\r\n-                  },\r\n-                ),\r\n-              ),\r\n-              Expanded(\r\n-                child: Container(\r\n-                  color: Theme.of(context).colorScheme.primaryContainer,\r\n-                  child: page,\r\n-                ),\r\n-              ),\r\n-            ],\r\n-          ),\r\n-        );\r\n-      }\r\n-    );\r\n-  }\r\n-}\r\n-\r\n-class GeneratorPage extends StatelessWidget {\r\n-  @override\r\n-  Widget build(BuildContext context) {\r\n-    var appState = context.watch<MyAppState>();\r\n-    var pair = appState.current;\r\n-\r\n-    IconData icon;\r\n-    if (appState.favorites.contains(pair)) {\r\n-      icon = Icons.favorite;\r\n-    } else {\r\n-      icon = Icons.favorite_border;\r\n-    }\r\n-\r\n-    return Center(\r\n-      child: Column(\r\n-        mainAxisAlignment: MainAxisAlignment.center,\r\n-        children: [\r\n-          BigCard(pair: pair),\r\n-          SizedBox(height: 10),\r\n-          Row(\r\n-            mainAxisSize: MainAxisSize.min,\r\n-            children: [\r\n-              ElevatedButton.icon(\r\n-                onPressed: () {\r\n-                  appState.toggleFavorite();\r\n-                },\r\n-                icon: Icon(icon),\r\n-                label: Text('Like'),\r\n-              ),\r\n-              SizedBox(width: 10),\r\n-              ElevatedButton(\r\n-                onPressed: () {\r\n-                  appState.getNext();\r\n-                },\r\n-                child: Text('Next'),\r\n-              ),\r\n-            ],\r\n-          ),\r\n-        ],\r\n-      ),\r\n-    );\r\n-  }\r\n-}\r\n-\r\n-class BigCard extends StatelessWidget {\r\n-  const BigCard({\r\n-    super.key,\r\n-    required this.pair,\r\n-  });\r\n-\r\n-  final WordPair pair;\r\n-\r\n-  @override\r\n-  Widget build(BuildContext context) {\r\n-    final theme = Theme.of(context);\r\n-    final style = theme.textTheme.displayMedium!.copyWith(\r\n-      color: theme.colorScheme.onPrimary,\r\n-    );\r\n-    return Card(\r\n-      color: theme.colorScheme.primary,\r\n-      child: Padding(\r\n-        padding: const EdgeInsets.all(20.0),\r\n-        child: Text(pair.asLowerCase,\r\n-            style: style, semanticsLabel: \"${pair.first} ${pair.second}}\"),\r\n-      ),\r\n-    );\r\n-  }\r\n-}\r\n-\r\n-\r\n-// ...\r\n-\r\n-class FavoritesPage extends StatelessWidget {\r\n-  @override\r\n-  Widget build(BuildContext context) {\r\n-    var appState = context.watch<MyAppState>();\r\n-\r\n-    if (appState.favorites.isEmpty) {\r\n-      return Center(\r\n-        child: Text('No favorites yet.'),\r\n-      );\r\n-    }\r\n-\r\n-    return ListView(\r\n-      children: [\r\n-        Padding(\r\n-          padding: const EdgeInsets.all(20),\r\n-          child: Text('You have '\r\n-              '${appState.favorites.length} favorites:'),\r\n-        ),\r\n-        for (var pair in appState.favorites)\r\n-          ListTile(\r\n-            leading: Icon(Icons.favorite),\r\n-            title: Text(pair.asLowerCase),\r\n-          ),\r\n-      ],\r\n-    );\r\n-  }\r\n+  runApp(Center());\r\n }\n\\ No newline at end of file\n"}, {"date": 1753178852776, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,4 +1,4 @@\n import 'package:flutter/material.dart';\r\n void main() {\r\n-  runApp(Center());\r\n+  runApp(const Center());\r\n }\n\\ No newline at end of file\n"}, {"date": 1753178877660, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,4 +1,9 @@\n import 'package:flutter/material.dart';\r\n void main() {\r\n-  runApp(const Center());\r\n+  runApp(const Center(\r\n+    child: Text(\r\n+      'Hello World',\r\n+      textDirection: TextDirection.ltr,\r\n+    ),\r\n+  ));\r\n }\n\\ No newline at end of file\n"}, {"date": 1753179040752, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -2,8 +2,8 @@\n void main() {\r\n   runApp(const Center(\r\n     child: Text(\r\n       'Hello World',\r\n-      textDirection: TextDirection.ltr,\r\n+      textDirection: TextDirection.rtl,\r\n     ),\r\n   ));\r\n }\n\\ No newline at end of file\n"}, {"date": 1753179053050, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -2,8 +2,8 @@\n void main() {\r\n   runApp(const Center(\r\n     child: Text(\r\n       'Hello World',\r\n-      textDirection: TextDirection.rtl,\r\n+      textDirection: TextDirection.ltr,\r\n     ),\r\n   ));\r\n }\n\\ No newline at end of file\n"}, {"date": 1753179066219, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,7 +1,7 @@\n import 'package:flutter/material.dart';\r\n void main() {\r\n-  runApp(const Center(\r\n+  runApp(Center(\r\n     child: Text(\r\n       'Hello World',\r\n       textDirection: TextDirection.ltr,\r\n     ),\r\n"}, {"date": 1753179143514, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -2,8 +2,8 @@\n void main() {\r\n   runApp(Center(\r\n     child: Text(\r\n       'Hello World',\r\n-      textDirection: TextDirection.ltr,\r\n+      textDirection: TextDirection.rtl,\r\n     ),\r\n   ));\r\n }\n\\ No newline at end of file\n"}, {"date": 1753179224020, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -2,8 +2,9 @@\n void main() {\r\n   runApp(Center(\r\n     child: Text(\r\n       'Hello World',\r\n-      textDirection: TextDirection.rtl,\r\n+      textDirection: TextDirection.ltr,\r\n+      style: ,\r\n     ),\r\n   ));\r\n }\n\\ No newline at end of file\n"}, {"date": 1753179281686, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -3,8 +3,10 @@\n   runApp(Center(\r\n     child: Text(\r\n       'Hello World',\r\n       textDirection: TextDirection.ltr,\r\n-      style: ,\r\n+      style: TextStyle(\r\n+        color: Colors.green\r\n+      ),\r\n     ),\r\n   ));\r\n }\n\\ No newline at end of file\n"}, {"date": 1753179363198, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -4,9 +4,9 @@\n     child: Text(\r\n       'Hello World',\r\n       textDirection: TextDirection.ltr,\r\n       style: TextStyle(\r\n-        color: Colors.green\r\n+        color: Color.fromRGBO(1, 1, 1, .8)\r\n       ),\r\n     ),\r\n   ));\r\n }\n\\ No newline at end of file\n"}, {"date": 1753179387282, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -4,9 +4,10 @@\n     child: Text(\r\n       'Hello World',\r\n       textDirection: TextDirection.ltr,\r\n       style: TextStyle(\r\n-        color: Color.fromRGBO(1, 1, 1, .8)\r\n+        color: Color.fromRGBO(1, 1, 1, .8),\r\n+        fontSize: 40.0\r\n       ),\r\n     ),\r\n   ));\r\n }\n\\ No newline at end of file\n"}, {"date": 1753179395851, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -5,9 +5,9 @@\n       'Hello World',\r\n       textDirection: TextDirection.ltr,\r\n       style: TextStyle(\r\n         color: Color.fromRGBO(1, 1, 1, .8),\r\n-        fontSize: 40.0\r\n+        fontSize: 40.0,\r\n       ),\r\n     ),\r\n   ));\r\n }\n\\ No newline at end of file\n"}, {"date": 1753179469451, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -4,9 +4,9 @@\n     child: Text(\r\n       'Hello World',\r\n       textDirection: TextDirection.ltr,\r\n       style: TextStyle(\r\n-        color: Color.fromRGBO(1, 1, 1, .8),\r\n+        color: Color.fromRGBO(255, 255, 255, .8),\r\n         fontSize: 40.0,\r\n       ),\r\n     ),\r\n   ));\r\n"}, {"date": 1753179775232, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,13 +1,11 @@\n import 'package:flutter/material.dart';\r\n void main() {\r\n-  runApp(Center(\r\n-    child: Text(\r\n-      'Hello World',\r\n-      textDirection: TextDirection.ltr,\r\n-      style: TextStyle(\r\n-        color: Color.fromRGBO(255, 255, 255, .8),\r\n-        fontSize: 40.0,\r\n+  runApp(MaterialApp(\r\n+    home: Scaffold(\r\n+      appBar: AppBar(title: const Text('MDM')),\r\n+      body: Center(\r\n+        child: Text('Hello World'),\r\n       ),\r\n-    ),\r\n+    )\r\n   ));\r\n }\n\\ No newline at end of file\n"}, {"date": 1753179881936, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -7,5 +7,6 @@\n         child: Text('Hello World'),\r\n       ),\r\n     )\r\n   ));\r\n-}\n\\ No newline at end of file\n+}\r\n+class\n\\ No newline at end of file\n"}, {"date": 1753234184171, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -7,6 +7,5 @@\n         child: Text('Hello World'),\r\n       ),\r\n     )\r\n   ));\r\n-}\r\n-class\n\\ No newline at end of file\n+}\n\\ No newline at end of file\n"}, {"date": 1753353095148, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -5,7 +5,8 @@\n       appBar: AppBar(title: const Text('MDM')),\r\n       body: Center(\r\n         child: Text('Hello World'),\r\n       ),\r\n-    )\r\n+    ),\r\n+\r\n   ));\r\n }\n\\ No newline at end of file\n"}, {"date": 1753353169686, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -5,8 +5,14 @@\n       appBar: AppBar(title: const Text('MDM')),\r\n       body: Center(\r\n         child: Text('Hello World'),\r\n       ),\r\n+      bottomNavigationBar: BottomNavigationBar(\r\n+        items: [\r\n+          BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Home'),\r\n+          BottomNavigationBarItem(icon: Icon(Icons.settings), label: 'Settings'),\r\n+        ],\r\n+      ),\r\n     ),\r\n \r\n   ));\r\n }\n\\ No newline at end of file\n"}, {"date": 1753353267182, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -9,8 +9,9 @@\n       bottomNavigationBar: BottomNavigationBar(\r\n         items: [\r\n           BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Home'),\r\n           BottomNavigationBarItem(icon: Icon(Icons.settings), label: 'Settings'),\r\n+          BottomNavigationBarItem(icon: Icon(Icons.person), label: 'Profile'),\r\n         ],\r\n       ),\r\n     ),\r\n \r\n"}, {"date": 1753353272278, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -9,9 +9,9 @@\n       bottomNavigationBar: BottomNavigationBar(\r\n         items: [\r\n           BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Home'),\r\n           BottomNavigationBarItem(icon: Icon(Icons.settings), label: 'Settings'),\r\n-          BottomNavigationBarItem(icon: Icon(Icons.person), label: 'Profile'),\r\n+          BottomNavigationBarItem(icon: Icon(Icons.person), label: 'Profile')\r\n         ],\r\n       ),\r\n     ),\r\n \r\n"}, {"date": 1753353434913, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -3,9 +3,11 @@\n   runApp(MaterialApp(\r\n     home: Scaffold(\r\n       appBar: AppBar(title: const Text('MDM')),\r\n       body: Center(\r\n-        child: Text('Hello World'),\r\n+        children: [\r\n+          \r\n+],\r\n       ),\r\n       bottomNavigationBar: BottomNavigationBar(\r\n         items: [\r\n           BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Home'),\r\n"}, {"date": 1753353685693, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -3,11 +3,9 @@\n   runApp(MaterialApp(\r\n     home: Scaffold(\r\n       appBar: AppBar(title: const Text('MDM')),\r\n       body: Center(\r\n-        children: [\r\n-          \r\n-],\r\n+        child: Text('Hello World'),\r\n       ),\r\n       bottomNavigationBar: BottomNavigationBar(\r\n         items: [\r\n           BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Home'),\r\n"}, {"date": 1753356130601, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,19 +1,81 @@\n import 'package:flutter/material.dart';\r\n+import 'package:battery_plus/battery_plus.dart'; // 引入电池插件\r\n+\r\n void main() {\r\n-  runApp(MaterialApp(\r\n-    home: Scaffold(\r\n+  runApp(const MyApp());\r\n+}\r\n+\r\n+class MyApp extends StatelessWidget {\r\n+  const MyApp({super.key});\r\n+\r\n+  @override\r\n+  Widget build(BuildContext context) {\r\n+    return MaterialApp(\r\n+      home: const MyHomePage(),\r\n+    );\r\n+  }\r\n+}\r\n+\r\n+class MyHomePage extends StatefulWidget {\r\n+  const MyHomePage({super.key});\r\n+\r\n+  @override\r\n+  State<MyHomePage> createState() => _MyHomePageState();\r\n+}\r\n+\r\n+class _MyHomePageState extends State<MyHomePage> {\r\n+  final Battery _battery = Battery();\r\n+  int _batteryLevel = -1;\r\n+\r\n+  Future<void> _getBatteryLevel() async {\r\n+    final level = await _battery.batteryLevel;\r\n+    setState(() {\r\n+      _batteryLevel = level;\r\n+    });\r\n+    ScaffoldMessenger.of(context).showSnackBar(\r\n+      SnackBar(content: Text('当前电量: $_batteryLevel%')),\r\n+    );\r\n+  }\r\n+\r\n+  int _selectedIndex = 0;\r\n+\r\n+  @override\r\n+  Widget build(BuildContext context) {\r\n+    return Scaffold(\r\n       appBar: AppBar(title: const Text('MDM')),\r\n       body: Center(\r\n-        child: Text('Hello World'),\r\n+        child: Column(\r\n+          mainAxisAlignment: MainAxisAlignment.center,\r\n+          children: [\r\n+            Text('Hello World'),\r\n+            const SizedBox(height: 20),\r\n+            ElevatedButton(\r\n+              onPressed: _getBatteryLevel,\r\n+              child: const Text('获取电池电量'),\r\n+            ),\r\n+            const SizedBox(height: 20),\r\n+            ElevatedButton(\r\n+              onPressed: () {\r\n+                ScaffoldMessenger.of(context).showSnackBar(\r\n+                  const SnackBar(content: Text('按钮被点击了！')),\r\n+                );\r\n+              },\r\n+              child: const Text('新按钮'),\r\n+            ),\r\n+            if (_batteryLevel != -1)\r\n+              Text('当前电量: $_batteryLevel%'),\r\n+          ],\r\n+        ),\r\n       ),\r\n       bottomNavigationBar: BottomNavigationBar(\r\n-        items: [\r\n+        currentIndex: _selectedIndex,\r\n+        onTap: (index) => setState(() => _selectedIndex = index),\r\n+        items: const [\r\n           BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Home'),\r\n           BottomNavigationBarItem(icon: Icon(Icons.settings), label: 'Settings'),\r\n-          BottomNavigationBarItem(icon: Icon(Icons.person), label: 'Profile')\r\n+          BottomNavigationBarItem(icon: Icon(Icons.person), label: 'Profile'),\r\n         ],\r\n       ),\r\n-    ),\r\n-\r\n-  ));\r\n+    );\r\n+  }\r\n }\n\\ No newline at end of file\n"}], "date": 1751811960693, "name": "Commit-0", "content": "import 'package:english_words/english_words.dart';\r\nimport 'package:flutter/material.dart';\r\nimport 'package:provider/provider.dart';\r\n\r\nvoid main() {\r\n  runApp(MyApp());\r\n}\r\n\r\nclass MyApp extends StatelessWidget {\r\n  const MyApp({super.key});\r\n\r\n  @override\r\n  Widget build(BuildContext context) {\r\n    return ChangeNotifierProvider(\r\n      create: (context) => MyAppState(),\r\n      child: MaterialApp(\r\n        title: 'Namer App',\r\n        theme: ThemeData(\r\n          useMaterial3: true,\r\n          colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepOrange),\r\n        ),\r\n        home: MyHomePage(),\r\n      ),\r\n    );\r\n  }\r\n}\r\n\r\nclass MyAppState extends ChangeNotifier {\r\n  var current = WordPair.random();\r\n}\r\n\r\nclass MyHomePage extends StatelessWidget {\r\n  @override\r\n  Widget build(BuildContext context) {\r\n    var appState = context.watch<MyAppState>();\r\n\r\n    return Scaffold(\r\n      body: Column(\r\n        children: [\r\n          Text('A random idea:'),\r\n          Text(appState.current.asLowerCase),\r\n        ],\r\n      ),\r\n    );\r\n  }\r\n}"}]}