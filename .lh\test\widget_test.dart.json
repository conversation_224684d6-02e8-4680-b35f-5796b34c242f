{"sourceFile": "test/widget_test.dart", "activeCommit": 0, "commits": [{"activePatchIndex": 3, "patches": [{"date": 1753178780650, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1753673222902, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -10,21 +10,20 @@\n \r\n import 'package:flutter_application_1/main1.dart';\r\n \r\n void main() {\r\n-  testWidgets('Counter increments smoke test', (WidgetTester tester) async {\r\n+  testWidgets('App loads correctly', (WidgetTester tester) async {\r\n     // Build our app and trigger a frame.\r\n     await tester.pumpWidget(const MyApp());\r\n \r\n-    // Verify that our counter starts at 0.\r\n-    expect(find.text('0'), findsOneWidget);\r\n-    expect(find.text('1'), findsNothing);\r\n+    // Verify that the app loads with the correct title\r\n+    expect(find.text('记账本'), findsOneWidget);\r\n \r\n-    // Tap the '+' icon and trigger a frame.\r\n-    await tester.tap(find.byIcon(Icons.add));\r\n-    await tester.pump();\r\n+    // Verify that the bottom navigation bar is present\r\n+    expect(find.text('首页'), findsOneWidget);\r\n+    expect(find.text('统计'), findsOneWidget);\r\n+    expect(find.text('设置'), findsOneWidget);\r\n \r\n-    // Verify that our counter has incremented.\r\n-    expect(find.text('0'), findsNothing);\r\n-    expect(find.text('1'), findsOneWidget);\r\n+    // Verify that the floating action button is present\r\n+    expect(find.byType(FloatingActionButton), findsOneWidget);\r\n   });\r\n }\r\n"}, {"date": 1753673337322, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -10,14 +10,14 @@\n \r\n import 'package:flutter_application_1/main1.dart';\r\n \r\n void main() {\r\n-  testWidgets('App loads correctly', (WidgetTester tester) async {\r\n+  testWidgets('App structure test', (WidgetTester tester) async {\r\n     // Build our app and trigger a frame.\r\n     await tester.pumpWidget(const MyApp());\r\n \r\n-    // Verify that the app loads with the correct title\r\n-    expect(find.text('记账本'), findsOneWidget);\r\n+    // Wait for the app to settle\r\n+    await tester.pumpAndSettle();\r\n \r\n     // Verify that the bottom navigation bar is present\r\n     expect(find.text('首页'), findsOneWidget);\r\n     expect(find.text('统计'), findsOneWidget);\r\n"}, {"date": 1753673535627, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -7,9 +7,9 @@\n \r\n import 'package:flutter/material.dart';\r\n import 'package:flutter_test/flutter_test.dart';\r\n \r\n-import 'package:flutter_application_1/main1.dart';\r\n+import 'package:flutter_application_1/main.dart';\r\n \r\n void main() {\r\n   testWidgets('App structure test', (WidgetTester tester) async {\r\n     // Build our app and trigger a frame.\r\n"}], "date": 1753178780650, "name": "Commit-0", "content": "// This is a basic Flutter widget test.\r\n//\r\n// To perform an interaction with a widget in your test, use the WidgetTester\r\n// utility in the flutter_test package. For example, you can send tap and scroll\r\n// gestures. You can also use WidgetTester to find child widgets in the widget\r\n// tree, read text, and verify that the values of widget properties are correct.\r\n\r\nimport 'package:flutter/material.dart';\r\nimport 'package:flutter_test/flutter_test.dart';\r\n\r\nimport 'package:flutter_application_1/main1.dart';\r\n\r\nvoid main() {\r\n  testWidgets('Counter increments smoke test', (WidgetTester tester) async {\r\n    // Build our app and trigger a frame.\r\n    await tester.pumpWidget(const MyApp());\r\n\r\n    // Verify that our counter starts at 0.\r\n    expect(find.text('0'), findsOneWidget);\r\n    expect(find.text('1'), findsNothing);\r\n\r\n    // Tap the '+' icon and trigger a frame.\r\n    await tester.tap(find.byIcon(Icons.add));\r\n    await tester.pump();\r\n\r\n    // Verify that our counter has incremented.\r\n    expect(find.text('0'), findsNothing);\r\n    expect(find.text('1'), findsOneWidget);\r\n  });\r\n}\r\n"}]}